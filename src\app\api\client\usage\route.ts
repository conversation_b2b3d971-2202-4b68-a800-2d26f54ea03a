import { NextRequest } from 'next/server'
import { withFirebaseAuth } from '@/lib/firebase-auth-middleware'
import { getUserByEmail, logUsage, checkDailyUsageLimit, updateDeviceActivity } from '@/lib/db'

// Track usage from client applications
export const POST = withFirebaseAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { 
      sessionStart, 
      sessionEnd, 
      platform, 
      deviceId, 
      deviceName,
      appVersion,
      features 
    } = body

    // Validate required fields
    if (!sessionStart || !sessionEnd || !platform || !deviceId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get user data
    const userData = await getUserByEmail(user.email)
    if (!userData) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Calculate session duration in seconds
    const startTime = new Date(sessionStart)
    const endTime = new Date(sessionEnd)
    const secondsUsed = Math.floor((endTime.getTime() - startTime.getTime()) / 1000)

    if (secondsUsed <= 0) {
      return new Response(
        JSON.stringify({ error: 'Invalid session duration' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check daily usage limit
    const usageCheck = await checkDailyUsageLimit(userData.id)
    if (!usageCheck.canUse) {
      return new Response(
        JSON.stringify({ 
          error: 'Daily usage limit exceeded',
          dailyLimitSeconds: usageCheck.dailyLimitSeconds,
          usedSeconds: usageCheck.usedSeconds
        }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Log the usage
    await logUsage({
      userId: userData.id,
      deviceId,
      sessionStart: startTime,
      sessionEnd: endTime,
      secondsUsed,
      platform: platform as 'windows' | 'macos' | 'ios' | 'android',
      appVersion,
      features
    })

    // Update device activity
    if (deviceName) {
      await updateDeviceActivity(deviceId)
    }

    // Get updated usage info
    const updatedUsageCheck = await checkDailyUsageLimit(userData.id)

    return new Response(
      JSON.stringify({
        success: true,
        sessionDuration: secondsUsed,
        dailyUsage: {
          usedSeconds: updatedUsageCheck.usedSeconds,
          remainingSeconds: updatedUsageCheck.remainingSeconds,
          dailyLimitSeconds: updatedUsageCheck.dailyLimitSeconds
        }
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error tracking usage:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

// Get current usage status
export const GET = withFirebaseAuth(async (request: NextRequest, user) => {
  try {
    // Get user data
    const userData = await getUserByEmail(user.email)
    if (!userData) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check daily usage limit
    const usageCheck = await checkDailyUsageLimit(userData.id)

    return new Response(
      JSON.stringify({
        user: {
          id: userData.id,
          email: userData.email,
          subscriptionPlan: userData.subscriptionPlan,
          subscriptionStatus: userData.subscriptionStatus
        },
        dailyUsage: {
          canUse: usageCheck.canUse,
          usedSeconds: usageCheck.usedSeconds,
          remainingSeconds: usageCheck.remainingSeconds,
          dailyLimitSeconds: usageCheck.dailyLimitSeconds
        }
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error getting usage status:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})
