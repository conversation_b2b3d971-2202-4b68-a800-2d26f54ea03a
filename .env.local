# Firebase Auth Configuration (No additional config needed - handled by Firebase SDK)

# Firebase Configuration (Admin SDK) - Get from Service Account JSON
FIREBASE_PROJECT_ID="speechpilot-f1495"
FIREBASE_PRIVATE_KEY_ID=""
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL="<EMAIL>"
FIREBASE_CLIENT_ID=""

# Firebase Configuration (Client SDK) - Get from Web App Config
NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyClbeEq7o4kXcDLcO40r7KhBAmIfIMVARA"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="speechpilot-f1495.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="speechpilot-f1495"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="speechpilot-f1495.firebasestorage.app"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="1005638354561"
NEXT_PUBLIC_FIREBASE_APP_ID="1:1005638354561:web:04959935ed09084298af80"
GOOGLE_ANALYTICS_MEASUREMENT_ID="G-57QFFBG4VP"

# Google OAuth Configuration (Get from Google Cloud Console)
# Replace with your actual Google OAuth credentials from the existing OAuth client
GOOGLE_CLIENT_ID="1005638354561-p9bu3b0q6tfgjdopoiratej25dg7aund.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY="pk_test_51RoRGqRZ0KQQwnzrqYucbabuHwNHzRs3EEptBjv1ww66Q3v7YF375U24b64G8MPnYIKyYF5HQiwProT2qnr5EvOe00BSVIqY5z"
STRIPE_SECRET_KEY="sk_test_51RoRGqRZ0KQQwnzrLo14jtgBocHBmaEcNjPsYbjtWqPUfFTFtfHcI9BfkyDXNsC9bnOjSwNAEeI89jvdTg3Pe7Bi00N2Rmk5e9"
STRIPE_WEBHOOK_SECRET="whsec_abfaf0ae244d65cd14e827de500e2c4fa3855cff422c4393d4fe214007162848"

# Stripe Subscription Price IDs for SpeechPilot (will be generated by setup script)
# Run: npm run setup-stripe to generate these automatically


# App Configuration
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="SpeechPilot"

# Download URLs for client apps (after payment)
NEXT_PUBLIC_WINDOWS_APP_URL="https://your-cdn.com/speechpilot-windows.exe"
NEXT_PUBLIC_MACOS_APP_URL="https://your-cdn.com/speechpilot-macos.dmg"

# Environment
NODE_ENV="development"

# Stripe Subscription Price IDs (Generated by setup-stripe.js)
STRIPE_PRICE_ID_STARTER_MONTHLY="price_1RoZKMRZ0KQQwnzrK6j68YRe"
STRIPE_PRICE_ID_STARTER_YEARLY="price_1RoZKNRZ0KQQwnzr4koknbV6"
STRIPE_PRICE_ID_PRO_MONTHLY="price_1RoZKNRZ0KQQwnzrsFzMldYu"
STRIPE_PRICE_ID_PRO_YEARLY="price_1RoZKORZ0KQQwnzr8zFJqEvt"
STRIPE_PRICE_ID_PREMIUM_MONTHLY="price_1RoZKORZ0KQQwnzrl2Eew2VK"
STRIPE_PRICE_ID_PREMIUM_YEARLY="price_1RoZKPRZ0KQQwnzrFYwoG8sd"
STRIPE_PRICE_ID_MAX_MONTHLY="price_1RoZKPRZ0KQQwnzrzw8RYyF0"
STRIPE_PRICE_ID_MAX_YEARLY="price_1RoZKQRZ0KQQwnzrxAgGnQ2Z"
