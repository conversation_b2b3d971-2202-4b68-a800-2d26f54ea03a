import { onRequest } from 'firebase-functions/v2/https';
import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import Stripe from 'stripe';

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
});

// Handle successful payments from Stripe webhooks
export const handleStripeWebhook = onRequest(
  { cors: true },
  async (request, response) => {
    try {
      const sig = request.headers['stripe-signature'] as string;
      const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;
      
      let event: Stripe.Event;
      
      try {
        event = stripe.webhooks.constructEvent(request.body, sig, endpointSecret);
      } catch (err) {
        console.error('Webhook signature verification failed:', err);
        response.status(400).send('Webhook signature verification failed');
        return;
      }
      
      // Handle the event
      switch (event.type) {
        case 'payment_intent.succeeded':
          await handlePaymentSuccess(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await handlePaymentFailure(event.data.object as Stripe.PaymentIntent);
          break;
        default:
          console.log(`Unhandled event type ${event.type}`);
      }
      
      response.json({ received: true });
    } catch (error) {
      console.error('Error processing webhook:', error);
      response.status(500).send('Internal server error');
    }
  }
);

// Handle successful payment
async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
  const { id, amount, currency, metadata } = paymentIntent;
  
  if (!metadata.userId || !metadata.productType) {
    console.error('Missing metadata in payment intent:', id);
    return;
  }
  
  // Create purchase record
  const purchaseData = {
    userId: metadata.userId,
    productType: metadata.productType as 'WINDOWS_APP' | 'MACOS_APP' | 'BUNDLE',
    stripePaymentIntentId: id,
    amount,
    currency,
    status: 'completed' as const,
    createdAt: new Date(),
    downloadUrls: generateDownloadUrls(metadata.productType),
  };
  
  await db.collection('purchases').add(purchaseData);
  
  console.log('Purchase created for user:', metadata.userId);
}

// Handle failed payment
async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
  const { id, amount, currency, metadata } = paymentIntent;
  
  if (!metadata.userId || !metadata.productType) {
    console.error('Missing metadata in payment intent:', id);
    return;
  }
  
  // Create failed purchase record
  const purchaseData = {
    userId: metadata.userId,
    productType: metadata.productType as 'WINDOWS_APP' | 'MACOS_APP' | 'BUNDLE',
    stripePaymentIntentId: id,
    amount,
    currency,
    status: 'failed' as const,
    createdAt: new Date(),
  };
  
  await db.collection('purchases').add(purchaseData);
  
  console.log('Failed purchase recorded for user:', metadata.userId);
}

// Generate download URLs based on product type
function generateDownloadUrls(productType: string) {
  const baseUrl = 'https://your-cdn.com/downloads';
  
  switch (productType) {
    case 'WINDOWS_APP':
      return { windows: `${baseUrl}/speechpilot-windows.exe` };
    case 'MACOS_APP':
      return { macos: `${baseUrl}/speechpilot-macos.dmg` };
    case 'BUNDLE':
      return {
        windows: `${baseUrl}/speechpilot-windows.exe`,
        macos: `${baseUrl}/speechpilot-macos.dmg`,
      };
    default:
      return {};
  }
}

// API endpoint for client apps to log usage
export const logClientUsage = onRequest(
  { cors: true },
  async (request, response) => {
    try {
      // Verify the request comes from authenticated client
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        response.status(401).send('Unauthorized');
        return;
      }
      
      // In a real implementation, you'd verify the JWT token here
      // For now, we'll assume the token contains the userId
      const token = authHeader.split('Bearer ')[1];
      // const decodedToken = await admin.auth().verifyIdToken(token);
      // const userId = decodedToken.uid;
      
      const { userId, sessionStart, sessionEnd, platform } = request.body;
      
      if (!userId || !sessionStart || !sessionEnd || !platform) {
        response.status(400).send('Missing required fields');
        return;
      }
      
      // Calculate seconds used
      const startTime = new Date(sessionStart);
      const endTime = new Date(sessionEnd);
      const secondsUsed = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);

      // Log usage
      const usageData = {
        userId,
        sessionStart: startTime,
        sessionEnd: endTime,
        secondsUsed, // Store exact seconds
        platform,
        createdAt: new Date(),
      };

      await db.collection('usage_logs').add(usageData);

      // Update user's total usage seconds
      const userRef = db.collection('users').doc(userId);
      const userDoc = await userRef.get();

      if (userDoc.exists) {
        const currentSeconds = userDoc.data()?.usageSeconds || 0;
        await userRef.update({
          usageSeconds: currentSeconds + secondsUsed,
        });
      }

      response.json({
        success: true,
        secondsLogged: secondsUsed,
        minutesLogged: Math.floor(secondsUsed / 60),
        hoursLogged: Math.floor(secondsUsed / 3600)
      });
    } catch (error) {
      console.error('Error logging usage:', error);
      response.status(500).send('Internal server error');
    }
  }
);
