import { NextRequest } from 'next/server'
import { withFirebaseAuth } from '@/lib/firebase-auth-middleware'
import { 
  getUserByEmail, 
  registerDevice, 
  getUserDevices, 
  checkDeviceLimit,
  deactivateDevice 
} from '@/lib/db'

// Register a new device
export const POST = withFirebaseAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { deviceId, deviceName, platform } = body

    // Validate required fields
    if (!deviceId || !deviceName || !platform) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: deviceId, deviceName, platform' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get user data
    const userData = await getUserByEmail(user.email)
    if (!userData) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check device limit
    const deviceCheck = await checkDeviceLimit(userData.id, platform)
    if (!deviceCheck.canAddDevice) {
      return new Response(
        JSON.stringify({ 
          error: 'Device limit exceeded',
          currentDeviceCount: deviceCheck.currentDeviceCount,
          maxDevices: deviceCheck.maxDevices,
          subscriptionPlan: userData.subscriptionPlan
        }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check if device already exists
    const existingDevices = await getUserDevices(userData.id)
    const existingDevice = existingDevices.find(d => d.deviceId === deviceId)

    if (existingDevice) {
      // Device already registered, just update activity
      return new Response(
        JSON.stringify({
          success: true,
          device: existingDevice,
          message: 'Device already registered'
        }),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Register new device
    const newDevice = await registerDevice({
      userId: userData.id,
      deviceId,
      deviceName,
      platform: platform as 'windows' | 'macos' | 'ios' | 'android',
      lastActiveAt: new Date(),
      isActive: true
    })

    return new Response(
      JSON.stringify({
        success: true,
        device: newDevice,
        deviceCount: deviceCheck.currentDeviceCount + 1,
        maxDevices: deviceCheck.maxDevices
      }),
      { status: 201, headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error registering device:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

// Get user's devices
export const GET = withFirebaseAuth(async (request: NextRequest, user) => {
  try {
    // Get user data
    const userData = await getUserByEmail(user.email)
    if (!userData) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get user's devices
    const devices = await getUserDevices(userData.id)

    return new Response(
      JSON.stringify({
        devices,
        subscriptionPlan: userData.subscriptionPlan,
        maxDevices: userData.subscriptionPlan === 'FREE' ? 0 : 
                   userData.subscriptionPlan === 'STARTER' ? 1 :
                   userData.subscriptionPlan === 'PRO' ? 2 :
                   userData.subscriptionPlan === 'PREMIUM' ? 5 : -1
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error getting devices:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

// Deactivate a device
export const DELETE = withFirebaseAuth(async (request: NextRequest, user) => {
  try {
    const url = new URL(request.url)
    const deviceId = url.searchParams.get('deviceId')

    if (!deviceId) {
      return new Response(
        JSON.stringify({ error: 'Missing deviceId parameter' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get user data
    const userData = await getUserByEmail(user.email)
    if (!userData) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Verify device belongs to user
    const devices = await getUserDevices(userData.id)
    const device = devices.find(d => d.deviceId === deviceId)

    if (!device) {
      return new Response(
        JSON.stringify({ error: 'Device not found or does not belong to user' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Deactivate device
    await deactivateDevice(device.id)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Device deactivated successfully'
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error deactivating device:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})
