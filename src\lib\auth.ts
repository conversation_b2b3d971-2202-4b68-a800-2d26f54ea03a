// Firebase Auth utilities
import { auth } from '@/lib/firebase'
import {
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth'
import { createUser, getUserById } from '@/lib/db'

const googleProvider = new GoogleAuthProvider()

export interface AuthUser {
  uid: string
  email: string | null
  name: string | null
  image: string | null
  subscriptionPlan?: string
  subscriptionStatus?: string
}

// Sign in with Google
export async function signInWithGoogle(): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
  try {
    // Clear any existing popup operations
    await auth.signOut().catch(() => {})

    // Configure Google provider with additional settings
    googleProvider.setCustomParameters({
      prompt: 'select_account'
    })

    console.log('🚀 Starting Google sign-in...')
    const result = await signInWithPopup(auth, googleProvider)
    const user = result.user

    console.log('✅ Firebase Auth User:', {
      uid: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      providerId: result.providerId
    })

    if (user.email) {
      try {
        // Check if user exists in our database using UID
        let existingUser = await getUserById(user.uid)

        if (!existingUser) {
          // Create new user in our database using the Firebase Auth UID
          await createUser({
            email: user.email,
            name: user.displayName || '',
            image: user.photoURL || '',
          }, user.uid)
          console.log('✅ New user created in database')
        } else {
          console.log('✅ Existing user found in database')
        }
      } catch (dbError: any) {
        console.error('❌ Database operation error:', dbError)
        // Continue with auth even if DB operation fails
      }
    }

    const authUser: AuthUser = {
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      image: user.photoURL,
    }

    return { success: true, user: authUser }
  } catch (error: any) {
    console.error('❌ Error signing in with Google:', error)

    // Handle specific error cases
    if (error.code === 'auth/cancelled-popup-request') {
      return { success: false, error: 'Sign-in was cancelled. Please try again.' }
    } else if (error.code === 'auth/popup-blocked') {
      return { success: false, error: 'Popup was blocked by browser. Please allow popups and try again.' }
    } else if (error.code === 'auth/popup-closed-by-user') {
      return { success: false, error: 'Sign-in was cancelled by user.' }
    }

    return { success: false, error: error.message }
  }
}

// Sign out
export async function signOut(): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🚪 Starting sign out...')
    await firebaseSignOut(auth)
    console.log('✅ User signed out successfully')
    return { success: true }
  } catch (error: any) {
    console.error('❌ Error signing out:', error)
    return { success: false, error: error.message }
  }
}

// Get current user
export function getCurrentUser(): Promise<User | null> {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe()
      resolve(user)
    })
  })
}

// Auth state listener
export function onAuthStateChange(callback: (user: User | null) => void) {
  return onAuthStateChanged(auth, callback)
}
