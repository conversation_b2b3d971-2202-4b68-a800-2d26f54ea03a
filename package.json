{"name": "speechpilot-web", "version": "1.0.0", "private": true, "description": "SpeechPilot Web - Payment and download platform for Windows and MacOS client apps", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe", "setup-stripe": "node scripts/setup-stripe.js"}, "dependencies": {"@auth/firebase-adapter": "^2.10.0", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@stripe/stripe-js": "^7.4.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.6.1", "firebase": "^11.1.0", "firebase-admin": "^12.7.0", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^4.24.11", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "typescript": "^5"}}