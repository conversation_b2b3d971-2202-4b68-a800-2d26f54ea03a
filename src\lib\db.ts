import { db } from './firebase'
import {
  collection,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  addDoc,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore'

// Types
export interface User {
  id: string
  email: string
  name?: string
  image?: string
  createdAt: Timestamp
  stripeCustomerId?: string
  usageSeconds?: number // Track client app usage in seconds
  subscriptionPlan?: 'FREE' | 'STARTER' | 'PRO' | 'PREMIUM' | 'MAX'
  subscriptionStatus?: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'incomplete' | null
  subscriptionEndDate?: Timestamp
  dailyUsageSeconds?: number // Track daily usage for limits
  lastUsageReset?: Timestamp // Track when daily usage was last reset
}

export interface Subscription {
  id: string
  userId: string
  stripeSubscriptionId: string
  stripePriceId: string
  plan: 'FREE' | 'STARTER' | 'PRO' | 'PREMIUM' | 'MAX'
  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'incomplete'
  currentPeriodStart: Timestamp
  currentPeriodEnd: Timestamp
  cancelAtPeriodEnd: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Purchase {
  id: string
  userId: string
  productType: 'SUBSCRIPTION' | 'CREDITS'
  stripePaymentIntentId?: string
  stripeSubscriptionId?: string
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed'
  createdAt: Timestamp
}

export interface Device {
  id: string
  userId: string
  deviceId: string // Unique device identifier from client app
  deviceName: string // User-friendly device name
  platform: 'windows' | 'macos' | 'ios' | 'android'
  lastActiveAt: Timestamp
  isActive: boolean
  createdAt: Timestamp
}

export interface UsageLog {
  id: string
  userId: string
  deviceId?: string // Link to device
  sessionStart: Timestamp
  sessionEnd?: Timestamp
  secondsUsed: number // Track usage in seconds for precision
  platform: 'windows' | 'macos' | 'ios' | 'android' // Add mobile platforms
  appVersion?: string // Track app version for analytics
  features?: string[] // Track which features were used
  createdAt: Timestamp
}

// User operations
export async function createUser(userData: Omit<User, 'id' | 'createdAt'>, userId?: string): Promise<User> {
  if (!db) throw new Error('Firebase not initialized')

  // Use provided userId (Firebase Auth UID) or generate new one
  const userRef = userId ? doc(db, 'users', userId) : doc(collection(db, 'users'))
  const user: User = {
    ...userData,
    id: userRef.id,
    createdAt: Timestamp.now(),
    usageSeconds: 0,
    subscriptionPlan: 'FREE', // Default to free plan
    dailyUsageSeconds: 0,
    lastUsageReset: Timestamp.now(),
  }

  await setDoc(userRef, user)
  return user
}

export async function getUserById(userId: string): Promise<User | null> {
  if (!db) throw new Error('Firebase not initialized')

  const userRef = doc(db, 'users', userId)
  const userSnap = await getDoc(userRef)

  if (userSnap.exists()) {
    return userSnap.data() as User
  }
  return null
}

export async function getUserByEmail(email: string): Promise<User | null> {
  if (!db) throw new Error('Firebase not initialized')

  const usersRef = collection(db, 'users')
  const q = query(usersRef, where('email', '==', email), limit(1))
  const querySnapshot = await getDocs(q)

  if (!querySnapshot.empty) {
    return querySnapshot.docs[0].data() as User
  }
  return null
}

export async function updateUser(userId: string, updates: Partial<User>): Promise<void> {
  if (!db) throw new Error('Firebase not initialized')

  const userRef = doc(db, 'users', userId)
  await updateDoc(userRef, updates)
}

// Subscription operations
export async function createSubscription(subscriptionData: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>): Promise<Subscription> {
  if (!db) throw new Error('Firebase not initialized')

  const subscriptionRef = doc(collection(db, 'subscriptions'))
  const subscription: Subscription = {
    ...subscriptionData,
    id: subscriptionRef.id,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  }

  await setDoc(subscriptionRef, subscription)
  return subscription
}

export async function updateSubscription(subscriptionId: string, updates: Partial<Subscription>): Promise<void> {
  if (!db) throw new Error('Firebase not initialized')

  const subscriptionRef = doc(db, 'subscriptions', subscriptionId)
  await updateDoc(subscriptionRef, {
    ...updates,
    updatedAt: Timestamp.now()
  })
}

export async function getUserActiveSubscription(userId: string): Promise<Subscription | null> {
  if (!db) throw new Error('Firebase not initialized')

  const subscriptionsRef = collection(db, 'subscriptions')
  const q = query(
    subscriptionsRef,
    where('userId', '==', userId),
    where('status', '==', 'active'),
    limit(1)
  )
  const querySnapshot = await getDocs(q)

  if (!querySnapshot.empty) {
    return querySnapshot.docs[0].data() as Subscription
  }
  return null
}

// Purchase operations
export async function createPurchase(purchaseData: Omit<Purchase, 'id' | 'createdAt'>): Promise<Purchase> {
  if (!db) throw new Error('Firebase not initialized')

  const purchaseRef = doc(collection(db, 'purchases'))
  const purchase: Purchase = {
    ...purchaseData,
    id: purchaseRef.id,
    createdAt: Timestamp.now(),
  }

  await setDoc(purchaseRef, purchase)
  return purchase
}

export async function getPurchasesByUserId(userId: string): Promise<Purchase[]> {
  if (!db) throw new Error('Firebase not initialized')

  const purchasesRef = collection(db, 'purchases')
  const q = query(
    purchasesRef,
    where('userId', '==', userId),
    orderBy('createdAt', 'desc')
  )
  const querySnapshot = await getDocs(q)

  return querySnapshot.docs.map(doc => doc.data() as Purchase)
}

export async function updatePurchase(purchaseId: string, updates: Partial<Purchase>): Promise<void> {
  if (!db) throw new Error('Firebase not initialized')

  const purchaseRef = doc(db, 'purchases', purchaseId)
  await updateDoc(purchaseRef, updates)
}

// Usage tracking operations
export async function logUsage(usageData: Omit<UsageLog, 'id' | 'createdAt'>): Promise<UsageLog> {
  if (!db) throw new Error('Firebase not initialized')

  const usageRef = doc(collection(db, 'usage_logs'))
  const usage: UsageLog = {
    ...usageData,
    id: usageRef.id,
    createdAt: Timestamp.now(),
  }

  await setDoc(usageRef, usage)

  // Update user's total usage seconds
  if (usage.secondsUsed > 0) {
    const userRef = doc(db, 'users', usage.userId)
    const userSnap = await getDoc(userRef)
    if (userSnap.exists()) {
      const currentSeconds = userSnap.data().usageSeconds || 0
      await updateDoc(userRef, {
        usageSeconds: currentSeconds + usage.secondsUsed
      })
    }
  }

  return usage
}

export async function getUserUsageSeconds(userId: string): Promise<number> {
  if (!db) throw new Error('Firebase not initialized')

  const userRef = doc(db, 'users', userId)
  const userSnap = await getDoc(userRef)

  if (userSnap.exists()) {
    return userSnap.data().usageSeconds || 0
  }
  return 0
}

// Helper function to convert seconds to human readable format
export function formatUsageTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`
  } else {
    return `${remainingSeconds}s`
  }
}

// Get usage statistics for a user
export async function getUserUsageStats(userId: string): Promise<{
  totalSeconds: number
  totalHours: number
  totalMinutes: number
  formattedTime: string
}> {
  const totalSeconds = await getUserUsageSeconds(userId)
  const totalHours = Math.floor(totalSeconds / 3600)
  const totalMinutes = Math.floor(totalSeconds / 60)

  return {
    totalSeconds,
    totalHours,
    totalMinutes,
    formattedTime: formatUsageTime(totalSeconds)
  }
}

// Get user's recent usage logs
export async function getRecentUsageLogs(userId: string, limitCount: number = 10): Promise<UsageLog[]> {
  if (!db) throw new Error('Firebase not initialized')

  const usageRef = collection(db, 'usage_logs')
  const q = query(
    usageRef,
    where('userId', '==', userId),
    orderBy('createdAt', 'desc'),
    limit(limitCount)
  )
  const querySnapshot = await getDocs(q)

  return querySnapshot.docs.map(doc => doc.data() as UsageLog)
}

// Check if user has active subscription
export async function hasActiveSubscription(userId: string): Promise<boolean> {
  if (!db) throw new Error('Firebase not initialized')

  const subscription = await getUserActiveSubscription(userId)
  if (!subscription) return false

  // Check if subscription is still valid
  const now = Timestamp.now()
  return subscription.currentPeriodEnd.toMillis() > now.toMillis()
}

// Get user's app access based on subscription status
export async function getUserAppAccess(userId: string): Promise<{
  hasAccess: boolean
  subscriptionStatus: string | null
  subscriptionEndDate: Timestamp | null
  downloadUrls: { windows?: string; macos?: string }
}> {
  if (!db) throw new Error('Firebase not initialized')

  const subscription = await getUserActiveSubscription(userId)

  if (!subscription || !await hasActiveSubscription(userId)) {
    return {
      hasAccess: false,
      subscriptionStatus: subscription?.status || null,
      subscriptionEndDate: subscription?.currentPeriodEnd || null,
      downloadUrls: {}
    }
  }

  // If user has active subscription, they get access to all platforms
  const downloadUrls = {
    windows: process.env.NEXT_PUBLIC_WINDOWS_APP_URL,
    macos: process.env.NEXT_PUBLIC_MACOS_APP_URL
  }

  return {
    hasAccess: true,
    subscriptionStatus: subscription.status,
    subscriptionEndDate: subscription.currentPeriodEnd,
    downloadUrls
  }
}

// Device management functions
export async function registerDevice(deviceData: Omit<Device, 'id' | 'createdAt'>): Promise<Device> {
  if (!db) throw new Error('Firebase not initialized')

  const deviceRef = doc(collection(db, 'devices'))
  const device: Device = {
    ...deviceData,
    id: deviceRef.id,
    createdAt: Timestamp.now(),
  }

  await setDoc(deviceRef, device)
  return device
}

export async function getUserDevices(userId: string): Promise<Device[]> {
  if (!db) throw new Error('Firebase not initialized')

  const devicesRef = collection(db, 'devices')
  const q = query(
    devicesRef,
    where('userId', '==', userId),
    where('isActive', '==', true),
    orderBy('lastActiveAt', 'desc')
  )
  const querySnapshot = await getDocs(q)

  return querySnapshot.docs.map(doc => doc.data() as Device)
}

export async function updateDeviceActivity(deviceId: string): Promise<void> {
  if (!db) throw new Error('Firebase not initialized')

  const deviceRef = doc(db, 'devices', deviceId)
  await updateDoc(deviceRef, {
    lastActiveAt: Timestamp.now()
  })
}

export async function deactivateDevice(deviceId: string): Promise<void> {
  if (!db) throw new Error('Firebase not initialized')

  const deviceRef = doc(db, 'devices', deviceId)
  await updateDoc(deviceRef, {
    isActive: false
  })
}

// Usage limit checking functions
export async function checkDailyUsageLimit(userId: string): Promise<{
  canUse: boolean
  remainingSeconds: number
  dailyLimitSeconds: number
  usedSeconds: number
}> {
  if (!db) throw new Error('Firebase not initialized')

  const userRef = doc(db, 'users', userId)
  const userSnap = await getDoc(userRef)

  if (!userSnap.exists()) {
    throw new Error('User not found')
  }

  const userData = userSnap.data() as User
  const plan = userData.subscriptionPlan || 'FREE'

  // Import plan limits (we'll need to import this from stripe.ts)
  const { SUBSCRIPTION_PLANS } = await import('./stripe')
  const planConfig = SUBSCRIPTION_PLANS[plan]

  // Check if we need to reset daily usage (new day)
  const now = Timestamp.now()
  const lastReset = userData.lastUsageReset || userData.createdAt
  const daysSinceReset = Math.floor((now.toMillis() - lastReset.toMillis()) / (1000 * 60 * 60 * 24))

  let dailyUsageSeconds = userData.dailyUsageSeconds || 0

  if (daysSinceReset >= 1) {
    // Reset daily usage
    dailyUsageSeconds = 0
    await updateDoc(userRef, {
      dailyUsageSeconds: 0,
      lastUsageReset: now
    })
  }

  const dailyLimitSeconds = planConfig.dailyLimitSeconds
  const remainingSeconds = dailyLimitSeconds === -1 ? -1 : Math.max(0, dailyLimitSeconds - dailyUsageSeconds)
  const canUse = dailyLimitSeconds === -1 || dailyUsageSeconds < dailyLimitSeconds

  return {
    canUse,
    remainingSeconds,
    dailyLimitSeconds,
    usedSeconds: dailyUsageSeconds
  }
}

export async function checkDeviceLimit(userId: string, platform: 'windows' | 'macos' | 'ios' | 'android'): Promise<{
  canAddDevice: boolean
  currentDeviceCount: number
  maxDevices: number
}> {
  if (!db) throw new Error('Firebase not initialized')

  const userRef = doc(db, 'users', userId)
  const userSnap = await getDoc(userRef)

  if (!userSnap.exists()) {
    throw new Error('User not found')
  }

  const userData = userSnap.data() as User
  const plan = userData.subscriptionPlan || 'FREE'

  // Import plan limits
  const { SUBSCRIPTION_PLANS, canAccessPlatform } = await import('./stripe')
  const planConfig = SUBSCRIPTION_PLANS[plan]

  // Check if user can access this platform
  if (!canAccessPlatform(plan, platform)) {
    return {
      canAddDevice: false,
      currentDeviceCount: 0,
      maxDevices: 0
    }
  }

  // Get current device count
  const devices = await getUserDevices(userId)
  const currentDeviceCount = devices.length
  const maxDevices = planConfig.maxDevices
  const canAddDevice = maxDevices === -1 || currentDeviceCount < maxDevices

  return {
    canAddDevice,
    currentDeviceCount,
    maxDevices
  }
}
