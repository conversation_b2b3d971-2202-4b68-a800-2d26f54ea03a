'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { 
  Download, 
  CheckCircle, 
  Calendar, 
  CreditCard,
  User,
  ExternalLink
} from 'lucide-react'
import { Purchase } from '@/lib/db'
import { PRODUCTS } from '@/lib/stripe'

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const searchParams = useSearchParams()
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [loading, setLoading] = useState(true)
  const [showSuccess, setShowSuccess] = useState(false)

  // Check for success parameter
  useEffect(() => {
    if (searchParams.get('success') === 'true') {
      setShowSuccess(true)
      // Remove success parameter from URL
      const url = new URL(window.location.href)
      url.searchParams.delete('success')
      url.searchParams.delete('product')
      window.history.replaceState({}, '', url.toString())
    }
  }, [searchParams])

  // Fetch user purchases
  useEffect(() => {
    if (session?.user) {
      fetchPurchases()
    }
  }, [session])

  const fetchPurchases = async () => {
    try {
      const response = await fetch('/api/purchases')
      if (response.ok) {
        const data = await response.json()
        setPurchases(data.purchases)
      }
    } catch (error) {
      console.error('Failed to fetch purchases:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDownload = (downloadUrl: string, productName: string) => {
    // In a real app, you might want to track downloads or provide secure download links
    window.open(downloadUrl, '_blank')
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="p-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">Please sign in to view your dashboard.</p>
          <Button onClick={() => window.location.href = '/'}>
            Go to Home
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Message */}
        {showSuccess && (
          <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
              <div>
                <h3 className="text-green-800 font-medium">Payment Successful!</h3>
                <p className="text-green-700 text-sm">
                  Your purchase has been completed. You can now download your software below.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {session.user?.name}!
          </h1>
          <p className="text-gray-600">
            Manage your SpeechPilot purchases and downloads
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* User Info */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <div className="flex items-center mb-4">
                <User className="w-5 h-5 text-gray-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Account Info</h2>
              </div>
              <div className="space-y-3">
                <div className="flex items-center">
                  {session.user?.image ? (
                    <img
                      src={session.user.image}
                      alt={session.user.name || 'User'}
                      className="w-12 h-12 rounded-full mr-3"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                      <User className="w-6 h-6 text-gray-600" />
                    </div>
                  )}
                  <div>
                    <p className="font-medium text-gray-900">{session.user?.name}</p>
                    <p className="text-sm text-gray-600">{session.user?.email}</p>
                  </div>
                </div>
                <div className="pt-3 border-t">
                  <p className="text-sm text-gray-600">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Member since {new Date().toLocaleDateString()}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Purchases & Downloads */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="flex items-center mb-6">
                <Download className="w-5 h-5 text-gray-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Your Purchases</h2>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600">Loading your purchases...</p>
                </div>
              ) : purchases.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No purchases yet</h3>
                  <p className="text-gray-600 mb-4">
                    You haven't made any purchases yet. Get started with SpeechPilot today!
                  </p>
                  <Button asChild>
                    <a href="/pricing">View Pricing</a>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {purchases.map((purchase) => {
                    const product = PRODUCTS[purchase.productType]
                    return (
                      <div
                        key={purchase.id}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 mb-1">
                              {product.name}
                            </h3>
                            <p className="text-sm text-gray-600 mb-2">
                              Purchased on {new Date(purchase.createdAt).toLocaleDateString()}
                            </p>
                            <div className="flex items-center">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                purchase.status === 'completed' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {purchase.status === 'completed' ? 'Completed' : 'Pending'}
                              </span>
                              <span className="ml-3 text-sm text-gray-600">
                                ${(purchase.amount / 100).toFixed(2)}
                              </span>
                            </div>
                          </div>
                          
                          {purchase.status === 'completed' && (
                            <div className="flex flex-col space-y-2">
                              {purchase.productType === 'BUNDLE' ? (
                                <>
                                  <Button
                                    size="sm"
                                    onClick={() => handleDownload(purchase.downloadUrls?.windows!, 'SpeechPilot for Windows')}
                                    className="flex items-center"
                                  >
                                    <Download className="w-4 h-4 mr-1" />
                                    Windows
                                    <ExternalLink className="w-3 h-3 ml-1" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDownload(purchase.downloadUrls?.macos!, 'SpeechPilot for macOS')}
                                    className="flex items-center"
                                  >
                                    <Download className="w-4 h-4 mr-1" />
                                    macOS
                                    <ExternalLink className="w-3 h-3 ml-1" />
                                  </Button>
                                </>
                              ) : (
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    const downloadUrl = purchase.productType === 'WINDOWS_APP' 
                                      ? purchase.downloadUrls?.windows 
                                      : purchase.downloadUrls?.macos
                                    if (downloadUrl) {
                                      handleDownload(downloadUrl, product.name)
                                    }
                                  }}
                                  className="flex items-center"
                                >
                                  <Download className="w-4 h-4 mr-1" />
                                  Download
                                  <ExternalLink className="w-3 h-3 ml-1" />
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </Card>
          </div>
        </div>

        {/* Support Section */}
        <div className="mt-8">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">Documentation</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Learn how to get the most out of SpeechPilot
                </p>
                <Button variant="outline" size="sm">
                  View Docs
                </Button>
              </div>
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">Support</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Get help from our support team
                </p>
                <Button variant="outline" size="sm">
                  Contact Support
                </Button>
              </div>
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">Community</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Join our user community
                </p>
                <Button variant="outline" size="sm">
                  Join Forum
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
