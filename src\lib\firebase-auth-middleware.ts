import { NextRequest } from 'next/server'
import { getAuth } from 'firebase-admin/auth'
import { initializeApp, getApps, cert } from 'firebase-admin/app'

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  initializeApp({
    credential: cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  })
}

export interface AuthenticatedUser {
  uid: string
  email: string
  name?: string
  picture?: string
}

/**
 * Verify Firebase ID Token from Authorization header
 * Used for client app API calls
 */
export async function verifyFirebaseToken(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }
    
    const idToken = authHeader.split('Bearer ')[1]
    
    if (!idToken) {
      return null
    }
    
    // Verify the ID token
    const decodedToken = await getAuth().verifyIdToken(idToken)
    
    return {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      name: decodedToken.name,
      picture: decodedToken.picture,
    }
  } catch (error) {
    console.error('Error verifying Firebase token:', error)
    return null
  }
}

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withFirebaseAuth(handler: (request: NextRequest, user: AuthenticatedUser) => Promise<Response>) {
  return async (request: NextRequest) => {
    const user = await verifyFirebaseToken(request)
    
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }
    
    return handler(request, user)
  }
}

/**
 * Get user from either NextAuth session (web) or Firebase token (client apps)
 */
export async function getAuthenticatedUser(request: NextRequest): Promise<AuthenticatedUser | null> {
  // First try Firebase token (for client apps)
  const firebaseUser = await verifyFirebaseToken(request)
  if (firebaseUser) {
    return firebaseUser
  }
  
  // If no Firebase token, this might be a web request with NextAuth session
  // In this case, the calling API route should handle NextAuth session separately
  return null
}
