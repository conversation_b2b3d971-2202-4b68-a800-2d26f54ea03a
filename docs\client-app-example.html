<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeechPilot 客戶端應用程式範例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
            background-color: #f0f8ff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #fff5f5;
            color: #dc3545;
        }
        .success {
            border-left-color: #28a745;
            background-color: #f0fff4;
            color: #28a745;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a8b;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .usage-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .device-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 SpeechPilot 客戶端應用程式範例</h1>
        
        <div id="authSection">
            <h2>🔐 登入狀態</h2>
            <div id="loginStatus" class="status">未登入</div>
            <button id="loginBtn" onclick="signInWithGoogle()">使用 Google 登入</button>
            <button id="logoutBtn" onclick="signOutUser()" style="display: none;">登出</button>
        </div>

        <div id="userSection" style="display: none;">
            <h2>👤 用戶資訊</h2>
            <div id="userInfo" class="usage-info"></div>
            
            <h2>📊 使用狀態</h2>
            <div id="usageInfo" class="usage-info"></div>
            <button onclick="refreshUsageInfo()">刷新使用狀態</button>
            
            <h2>📱 設備管理</h2>
            <div id="deviceInfo" class="device-info"></div>
            <button onclick="registerCurrentDevice()">註冊當前設備</button>
            <button onclick="getDevices()">獲取設備列表</button>
            
            <h2>⏱️ 使用追蹤</h2>
            <div id="sessionInfo" class="usage-info"></div>
            <button id="startSessionBtn" onclick="startSession()">開始使用</button>
            <button id="endSessionBtn" onclick="endSession()" disabled>結束使用</button>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase 配置
        const firebaseConfig = {
            apiKey: "AIzaSyClbeEq7o4kXcDLcO40r7KhBAmIfIMVARA",
            authDomain: "speechpilot-f1495.firebaseapp.com",
            projectId: "speechpilot-f1495",
            storageBucket: "speechpilot-f1495.firebasestorage.app",
            messagingSenderId: "1005638354561",
            appId: "1:1005638354561:web:04959935ed09084298af80"
        };

        // 初始化 Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const googleProvider = new GoogleAuthProvider();

        // 全域變數
        let currentUser = null;
        let sessionStartTime = null;
        const API_BASE = 'http://localhost:3000/api/client';

        // 生成設備 ID
        function getDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = 'device_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('deviceId', deviceId);
            }
            return deviceId;
        }

        // 獲取平台資訊
        function getPlatform() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Windows')) return 'windows';
            if (userAgent.includes('Mac')) return 'macos';
            if (userAgent.includes('iPhone') || userAgent.includes('iPad')) return 'ios';
            if (userAgent.includes('Android')) return 'android';
            return 'unknown';
        }

        // API 調用輔助函數
        async function callAPI(endpoint, method = 'GET', data = null) {
            if (!currentUser) {
                throw new Error('用戶未登入');
            }

            const token = await currentUser.getIdToken();
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(`${API_BASE}${endpoint}`, options);
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || '請求失敗');
            }

            return response.json();
        }

        // 登入函數
        window.signInWithGoogle = async function() {
            try {
                const result = await signInWithPopup(auth, googleProvider);
                console.log('登入成功:', result.user);
            } catch (error) {
                console.error('登入失敗:', error);
                updateStatus('登入失敗: ' + error.message, 'error');
            }
        };

        // 登出函數
        window.signOutUser = async function() {
            try {
                await signOut(auth);
                console.log('登出成功');
            } catch (error) {
                console.error('登出失敗:', error);
                updateStatus('登出失敗: ' + error.message, 'error');
            }
        };

        // 刷新使用狀態
        window.refreshUsageInfo = async function() {
            try {
                const data = await callAPI('/usage');
                document.getElementById('usageInfo').innerHTML = `
                    <strong>訂閱計劃:</strong> ${data.user.subscriptionPlan}<br>
                    <strong>今日已使用:</strong> ${Math.floor(data.dailyUsage.usedSeconds / 60)} 分鐘<br>
                    <strong>今日剩餘:</strong> ${data.dailyUsage.remainingSeconds === -1 ? '無限制' : Math.floor(data.dailyUsage.remainingSeconds / 60) + ' 分鐘'}<br>
                    <strong>每日限制:</strong> ${data.dailyUsage.dailyLimitSeconds === -1 ? '無限制' : Math.floor(data.dailyUsage.dailyLimitSeconds / 60) + ' 分鐘'}
                `;
            } catch (error) {
                updateStatus('獲取使用狀態失敗: ' + error.message, 'error');
            }
        };

        // 註冊當前設備
        window.registerCurrentDevice = async function() {
            try {
                const data = await callAPI('/devices', 'POST', {
                    deviceId: getDeviceId(),
                    deviceName: `${getPlatform()} 設備`,
                    platform: getPlatform()
                });
                updateStatus('設備註冊成功', 'success');
                getDevices();
            } catch (error) {
                updateStatus('設備註冊失敗: ' + error.message, 'error');
            }
        };

        // 獲取設備列表
        window.getDevices = async function() {
            try {
                const data = await callAPI('/devices');
                const deviceList = data.devices.map(device => 
                    `${device.deviceName} (${device.platform}) - ${device.isActive ? '活躍' : '非活躍'}`
                ).join('<br>');
                
                document.getElementById('deviceInfo').innerHTML = `
                    <strong>已註冊設備 (${data.devices.length}/${data.maxDevices === -1 ? '無限制' : data.maxDevices}):</strong><br>
                    ${deviceList || '無設備'}
                `;
            } catch (error) {
                updateStatus('獲取設備列表失敗: ' + error.message, 'error');
            }
        };

        // 開始使用會話
        window.startSession = function() {
            sessionStartTime = new Date();
            document.getElementById('startSessionBtn').disabled = true;
            document.getElementById('endSessionBtn').disabled = false;
            document.getElementById('sessionInfo').innerHTML = `
                <strong>會話狀態:</strong> 進行中<br>
                <strong>開始時間:</strong> ${sessionStartTime.toLocaleString()}
            `;
        };

        // 結束使用會話
        window.endSession = async function() {
            if (!sessionStartTime) return;

            try {
                const sessionEnd = new Date();
                const data = await callAPI('/usage', 'POST', {
                    sessionStart: sessionStartTime.toISOString(),
                    sessionEnd: sessionEnd.toISOString(),
                    platform: getPlatform(),
                    deviceId: getDeviceId(),
                    deviceName: `${getPlatform()} 設備`,
                    appVersion: '1.0.0',
                    features: ['speech-to-text']
                });

                document.getElementById('sessionInfo').innerHTML = `
                    <strong>會話狀態:</strong> 已結束<br>
                    <strong>使用時長:</strong> ${Math.floor(data.sessionDuration / 60)} 分鐘 ${data.sessionDuration % 60} 秒<br>
                    <strong>今日已使用:</strong> ${Math.floor(data.dailyUsage.usedSeconds / 60)} 分鐘
                `;

                sessionStartTime = null;
                document.getElementById('startSessionBtn').disabled = false;
                document.getElementById('endSessionBtn').disabled = true;

                updateStatus('使用記錄已保存', 'success');
                refreshUsageInfo();
            } catch (error) {
                updateStatus('保存使用記錄失敗: ' + error.message, 'error');
            }
        };

        // 更新狀態顯示
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('loginStatus');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // 監聽登入狀態變化
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            
            if (user) {
                document.getElementById('loginStatus').textContent = `已登入: ${user.email}`;
                document.getElementById('loginStatus').className = 'status success';
                document.getElementById('loginBtn').style.display = 'none';
                document.getElementById('logoutBtn').style.display = 'inline-block';
                document.getElementById('userSection').style.display = 'block';
                
                document.getElementById('userInfo').innerHTML = `
                    <strong>Email:</strong> ${user.email}<br>
                    <strong>名稱:</strong> ${user.displayName || '未設置'}<br>
                    <strong>UID:</strong> ${user.uid}
                `;

                // 自動刷新使用狀態和設備資訊
                refreshUsageInfo();
                getDevices();
            } else {
                document.getElementById('loginStatus').textContent = '未登入';
                document.getElementById('loginStatus').className = 'status';
                document.getElementById('loginBtn').style.display = 'inline-block';
                document.getElementById('logoutBtn').style.display = 'none';
                document.getElementById('userSection').style.display = 'none';
            }
        });
    </script>
</body>
</html>
