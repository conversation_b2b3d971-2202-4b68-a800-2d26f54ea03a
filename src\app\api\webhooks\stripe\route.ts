import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe, PRODUCTS } from '@/lib/stripe'
import { createPurchase, updateUser, getUserByEmail } from '@/lib/db'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe signature' },
        { status: 400 }
      )
    }

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        
        if (session.payment_status === 'paid') {
          const { userId, productType } = session.metadata!
          
          // Get user by email if userId not available
          let user
          if (userId) {
            user = { id: userId }
          } else if (session.customer_email) {
            user = await getUserByEmail(session.customer_email)
          }

          if (!user) {
            console.error('User not found for completed payment')
            break
          }

          // Create purchase record
          const product = PRODUCTS[productType as keyof typeof PRODUCTS]
          await createPurchase({
            userId: user.id,
            productType: productType as any,
            stripePaymentIntentId: session.payment_intent as string,
            amount: session.amount_total!,
            currency: session.currency!,
            status: 'completed',
            downloadUrls: productType === 'BUNDLE' 
              ? product.downloadUrls 
              : { [productType.toLowerCase().replace('_app', '')]: product.downloadUrl }
          })

          console.log(`✅ Purchase completed for user ${user.id}: ${productType}`)
        }
        break
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        console.log(`❌ Payment failed: ${paymentIntent.id}`)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}
