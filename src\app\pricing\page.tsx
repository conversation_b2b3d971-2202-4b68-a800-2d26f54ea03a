'use client'

import { useState } from 'react'
import { useSession, signIn } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { CheckCircle, Download, Star } from 'lucide-react'
import { PRODUCTS, ProductType } from '@/lib/stripe'

export default function PricingPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState<string | null>(null)

  const handlePurchase = async (productType: ProductType) => {
    if (!session) {
      signIn('google')
      return
    }

    setLoading(productType)
    
    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productType }),
      })

      const data = await response.json()

      if (data.url) {
        window.location.href = data.url
      } else {
        throw new Error('Failed to create checkout session')
      }
    } catch (error) {
      console.error('Purchase error:', error)
      alert('Failed to start checkout. Please try again.')
    } finally {
      setLoading(null)
    }
  }

  const products = [
    {
      key: 'WINDOWS_APP' as ProductType,
      ...PRODUCTS.WINDOWS_APP,
      platform: 'Windows',
      icon: (
        <svg className="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"/>
        </svg>
      ),
      features: [
        'Windows 10/11 compatible',
        'Microsoft Office integration',
        'System-wide hotkeys',
        'Real-time transcription',
        'Offline processing',
        'Custom vocabulary',
        'Voice commands',
        'Export to multiple formats'
      ]
    },
    {
      key: 'MACOS_APP' as ProductType,
      ...PRODUCTS.MACOS_APP,
      platform: 'macOS',
      icon: (
        <svg className="w-8 h-8 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.624 5.367 11.99 11.988 11.99s11.99-5.366 11.99-11.99C24.007 5.367 18.641.001 12.017.001z"/>
        </svg>
      ),
      features: [
        'macOS 11+ compatible',
        'Apple Silicon optimized',
        'Spotlight integration',
        'Real-time transcription',
        'Offline processing',
        'Custom vocabulary',
        'Voice commands',
        'Export to multiple formats'
      ]
    },
    {
      key: 'BUNDLE' as ProductType,
      ...PRODUCTS.BUNDLE,
      platform: 'Both Platforms',
      icon: (
        <div className="flex space-x-1">
          <svg className="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
            <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"/>
          </svg>
          <svg className="w-6 h-6 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.624 5.367 11.99 11.988 11.99s11.99-5.366 11.99-11.99C24.007 5.367 18.641.001 12.017.001z"/>
          </svg>
        </div>
      ),
      features: [
        'Windows & macOS versions',
        'Cross-platform sync',
        'All platform features',
        'Priority support',
        'Future updates included',
        'Best value option',
        'Save $19 vs individual',
        'Lifetime license'
      ],
      popular: true
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Choose Your Platform
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get SpeechPilot for your preferred platform or save with our bundle. 
            One-time purchase, lifetime access, no subscriptions.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {products.map((product) => (
            <Card 
              key={product.key} 
              className={`relative p-8 ${product.popular ? 'ring-2 ring-blue-500 shadow-xl' : 'shadow-lg'}`}
            >
              {product.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <div className="flex justify-center mb-4">
                  {product.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {product.name}
                </h3>
                <p className="text-gray-600 mb-4">{product.description}</p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">
                    ${(product.price / 100).toFixed(0)}
                  </span>
                  <span className="text-gray-500 ml-1">one-time</span>
                </div>
                {product.key === 'BUNDLE' && (
                  <p className="text-green-600 font-medium text-sm">
                    Save $19 compared to buying separately
                  </p>
                )}
              </div>

              <ul className="space-y-3 mb-8">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                onClick={() => handlePurchase(product.key)}
                disabled={loading === product.key}
                className={`w-full ${
                  product.popular 
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700' 
                    : ''
                }`}
                size="lg"
              >
                {loading === product.key ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Processing...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <Download className="w-4 h-4 mr-2" />
                    {session ? 'Buy Now' : 'Sign In & Buy'}
                  </div>
                )}
              </Button>
            </Card>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            What's Included
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              'Real-time transcription',
              'Offline processing',
              'Multi-language support',
              'Custom vocabulary',
              'Voice commands',
              'Export options',
              'Lifetime updates',
              '30-day guarantee'
            ].map((feature, index) => (
              <div key={index} className="flex items-center">
                <Star className="w-5 h-5 text-yellow-400 mr-3" />
                <span className="text-gray-700">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is this a one-time purchase?
              </h3>
              <p className="text-gray-600">
                Yes! Unlike other speech-to-text services, SpeechPilot is a one-time purchase with no monthly fees or subscriptions.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Do I get lifetime updates?
              </h3>
              <p className="text-gray-600">
                Yes, your purchase includes all future updates and improvements to SpeechPilot at no additional cost.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                What if I'm not satisfied?
              </h3>
              <p className="text-gray-600">
                We offer a 30-day money-back guarantee. If you're not completely satisfied, we'll refund your purchase.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Does it work offline?
              </h3>
              <p className="text-gray-600">
                Yes! SpeechPilot processes everything locally on your device, so it works perfectly without an internet connection.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
