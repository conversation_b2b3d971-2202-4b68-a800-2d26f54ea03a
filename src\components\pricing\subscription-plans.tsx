'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Check, Star } from 'lucide-react'
import { SUBSCRIPTION_PLANS, getYearlySavingsPercentage, getPriceAmount } from '@/lib/stripe'

interface SubscriptionPlansProps {
  onSubscribe?: (planId: string, billingPeriod: 'monthly' | 'yearly') => void
}

export function SubscriptionPlans({ onSubscribe }: SubscriptionPlansProps) {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly')

  const formatPrice = (cents: number) => {
    return (cents / 100).toFixed(2)
  }

  const handleSubscribe = (planKey: string) => {
    if (planKey === 'FREE') return
    onSubscribe?.(planKey, billingPeriod)
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            選擇適合你的方案
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            從免費開始，隨時升級到更強大的功能
          </p>
          
          {/* Billing Period Toggle */}
          <div className="flex items-center justify-center mb-8">
            <div className="bg-white rounded-lg p-1 shadow-sm border">
              <button
                onClick={() => setBillingPeriod('monthly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingPeriod === 'monthly'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                月付
              </button>
              <button
                onClick={() => setBillingPeriod('yearly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingPeriod === 'yearly'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                年付
                <span className="ml-1 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                  省錢
                </span>
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {Object.entries(SUBSCRIPTION_PLANS).map(([planKey, plan]) => {
            const isPopular = planKey === 'PRO'
            const price = getPriceAmount(planKey as any, billingPeriod)
            const savings = billingPeriod === 'yearly' ? getYearlySavingsPercentage(planKey as any) : 0
            
            return (
              <div
                key={planKey}
                className={`relative bg-white rounded-lg shadow-sm border-2 p-6 ${
                  isPopular 
                    ? 'border-blue-500 ring-2 ring-blue-200 transform scale-105' 
                    : 'border-gray-200 hover:border-gray-300'
                } transition-all duration-200`}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
                      <Star className="w-3 h-3 mr-1" />
                      最受歡迎
                    </div>
                  </div>
                )}

                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  
                  <div className="mb-4">
                    {planKey === 'FREE' ? (
                      <div className="text-3xl font-bold text-gray-900">免費</div>
                    ) : (
                      <>
                        <div className="text-3xl font-bold text-gray-900">
                          ${formatPrice(price)}
                        </div>
                        <div className="text-sm text-gray-500">
                          /{billingPeriod === 'monthly' ? '月' : '年'}
                        </div>
                        {billingPeriod === 'yearly' && savings > 0 && (
                          <div className="text-xs text-green-600 font-medium mt-1">
                            比月付省 {savings}%
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mb-6">
                    {plan.description}
                  </p>

                  <Button
                    onClick={() => handleSubscribe(planKey)}
                    disabled={planKey === 'FREE'}
                    className={`w-full mb-6 ${
                      isPopular
                        ? 'bg-blue-600 hover:bg-blue-700'
                        : planKey === 'FREE'
                        ? 'bg-gray-300 cursor-not-allowed'
                        : 'bg-gray-900 hover:bg-gray-800'
                    }`}
                  >
                    {planKey === 'FREE' ? '當前方案' : '選擇方案'}
                  </Button>

                  <ul className="space-y-3 text-sm">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
