import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'
import { 
  Mic, 
  Download, 
  Zap, 
  Shield, 
  Globe, 
  Clock,
  CheckCircle,
  Star
} from 'lucide-react'

export default function HomePage() {
  const features = [
    {
      icon: <Mic className="w-6 h-6" />,
      title: "Advanced Speech Recognition",
      description: "Industry-leading accuracy with support for multiple languages and accents"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Real-time Processing",
      description: "Instant transcription as you speak with minimal latency"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Privacy First",
      description: "All processing happens locally on your device - your data never leaves your computer"
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: "Multi-language Support",
      description: "Support for 50+ languages with automatic language detection"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Productivity Boost",
      description: "Type 3x faster than traditional typing with voice commands and shortcuts"
    },
    {
      icon: <Download className="w-6 h-6" />,
      title: "Easy Integration",
      description: "Works seamlessly with your favorite applications and workflows"
    }
  ]

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Content Writer",
      content: "SpeechPilot has revolutionized my writing process. I can now create content 3x faster!",
      rating: 5
    },
    {
      name: "Michael Chen",
      role: "Software Developer",
      content: "Perfect for code documentation and meeting notes. The accuracy is incredible.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      role: "Journalist",
      content: "Essential tool for interviews and article writing. Saves me hours every day.",
      rating: 5
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Transform Your Voice Into
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {" "}Perfect Text
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Professional speech-to-text software for Windows and macOS. 
              Experience industry-leading accuracy, real-time processing, and complete privacy.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Link href="/pricing">Get Started - $49</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#features">Learn More</Link>
              </Button>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              One-time purchase • No subscription required • 30-day money-back guarantee
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose SpeechPilot?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Built for professionals who demand accuracy, speed, and privacy in their speech-to-text workflow.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white mr-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
                </div>
                <p className="text-gray-600">{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Support */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available for Your Platform
            </h2>
            <p className="text-xl text-gray-600">
              Native applications optimized for Windows and macOS
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Windows</h3>
              <p className="text-gray-600 mb-4">
                Optimized for Windows 10 and 11. Seamless integration with Microsoft Office and other productivity tools.
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• Windows 10/11 compatible</li>
                <li>• Office integration</li>
                <li>• System-wide hotkeys</li>
              </ul>
            </Card>
            
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.624 5.367 11.99 11.988 11.99s11.99-5.366 11.99-11.99C24.007 5.367 18.641.001 12.017.001zM8.948 2.684c-.064 0-.125.016-.125.016s.063-.016.125-.016zm2.139-.048c-.016 0-.031 0-.047.016.016-.016.031-.016.047-.016zm.859.032c-.032 0-.063.016-.063.016s.031-.016.063-.016z"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">macOS</h3>
              <p className="text-gray-600 mb-4">
                Native macOS application with full support for macOS features and Apple Silicon processors.
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• macOS 11+ compatible</li>
                <li>• Apple Silicon optimized</li>
                <li>• Spotlight integration</li>
              </ul>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Professionals
            </h2>
            <p className="text-xl text-gray-600">
              See what our users are saying about SpeechPilot
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Productivity?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of professionals who have already made the switch to SpeechPilot.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="/pricing">Get SpeechPilot Now</Link>
            </Button>
          </div>
          <div className="flex items-center justify-center mt-8 space-x-6 text-blue-100">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              <span>30-day guarantee</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              <span>One-time purchase</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              <span>Instant download</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
