import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Providers } from '@/components/providers'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'SpeechPilot - Professional Speech-to-Text Software',
  description: 'Transform your voice into text with industry-leading accuracy. Professional speech-to-text software for Windows and macOS.',
  keywords: ['speech to text', 'voice recognition', 'transcription', 'dictation', 'productivity'],
  authors: [{ name: 'SpeechPilot Team' }],
  creator: 'SpeechPilot',
  publisher: 'SpeechPilot',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL,
    title: 'SpeechPilot - Professional Speech-to-Text Software',
    description: 'Transform your voice into text with industry-leading accuracy. Professional speech-to-text software for Windows and macOS.',
    siteName: 'SpeechPilot',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SpeechPilot - Professional Speech-to-Text Software',
    description: 'Transform your voice into text with industry-leading accuracy. Professional speech-to-text software for Windows and macOS.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased">
        <Providers>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </Providers>
      </body>
    </html>
  )
}
