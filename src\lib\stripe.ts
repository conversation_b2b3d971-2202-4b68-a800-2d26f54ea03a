import Stripe from 'stripe'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
})

// REVIEW Subscription.md for the setup
// Subscription plans for SpeechPilot based on Subscription.md
export const SUBSCRIPTION_PLANS = {
  FREE: {
    name: 'Free',
    description: '5 minutes per day',
    price: 0,
    priceId: null, // Free plan doesn't need Stripe price ID
    dailyLimitSeconds: 300, // 5 minutes = 300 seconds
    platforms: [] as const,
    maxDevices: 0,
    features: ['5 minutes per day', 'Basic speech-to-text']
  },
  STARTER: {
    name: 'Starter',
    description: '1 hour per day, client apps only',
    monthlyPrice: 999, // $9.99 in cents
    yearlyPrice: 9900, // $99.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_STARTER_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_STARTER_YEARLY!,
    dailyLimitSeconds: 3600, // 1 hour = 3600 seconds
    platforms: ['windows', 'macos'] as const,
    maxDevices: 1,
    features: [
      '1 hour per day (30 hours per month)',
      'Access to client app only (Windows, MacOS)',
      'Maximum device limit: 1'
    ]
  },
  PRO: {
    name: 'Pro',
    description: '2 hours per day, access to all platforms',
    monthlyPrice: 1999, // $19.99 in cents
    yearlyPrice: 19900, // $199.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_PRO_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_PRO_YEARLY!,
    dailyLimitSeconds: 7200, // 2 hours = 7200 seconds
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: 2,
    features: [
      '2 hours per day (60 hours per month)',
      'Access to all platforms (Windows, MacOS, iOS, Android)',
      'Maximum device limit: 2'
    ]
  },
  PREMIUM: {
    name: 'Premium',
    description: '8 hours per day, all platforms',
    monthlyPrice: 5999, // $59.99 in cents
    yearlyPrice: 55900, // $559.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_PREMIUM_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_PREMIUM_YEARLY!,
    dailyLimitSeconds: 28800, // 8 hours = 28800 seconds
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: 5,
    features: [
      '8 hours per day (240 hours per month)',
      'Access to all platforms (Windows, MacOS, iOS, Android)',
      'Maximum device limit: 5'
    ]
  },
  MAX: {
    name: 'Max',
    description: 'Unlimited usage, all platforms',
    monthlyPrice: 12999, // $129.99 in cents
    yearlyPrice: 109900, // $1099.00 in cents
    monthlyPriceId: process.env.STRIPE_PRICE_ID_MAX_MONTHLY!,
    yearlyPriceId: process.env.STRIPE_PRICE_ID_MAX_YEARLY!,
    dailyLimitSeconds: -1, // -1 means unlimited
    platforms: ['windows', 'macos', 'ios', 'android'] as const,
    maxDevices: -1, // -1 means unlimited
    features: [
      'Unlimited hours per month',
      'Access to all platforms (Windows, MacOS, iOS, Android)',
      'Unlimited device limit'
    ]
  }
} as const

export type SubscriptionPlan = keyof typeof SUBSCRIPTION_PLANS

// Helper functions for subscription plan validation
export function getPlanLimits(plan: SubscriptionPlan) {
  return SUBSCRIPTION_PLANS[plan]
}

export function canAccessPlatform(userPlan: SubscriptionPlan, platform: 'windows' | 'macos' | 'ios' | 'android'): boolean {
  const planConfig = SUBSCRIPTION_PLANS[userPlan]
  return (planConfig.platforms as readonly string[]).includes(platform) || planConfig.platforms.length === 0
}

export function hasReachedDeviceLimit(userPlan: SubscriptionPlan, currentDeviceCount: number): boolean {
  const planConfig = SUBSCRIPTION_PLANS[userPlan]
  if (planConfig.maxDevices === -1) return false // Unlimited
  return currentDeviceCount >= planConfig.maxDevices
}

export function hasReachedDailyLimit(userPlan: SubscriptionPlan, dailyUsageSeconds: number): boolean {
  const planConfig = SUBSCRIPTION_PLANS[userPlan]
  if (planConfig.dailyLimitSeconds === -1) return false // Unlimited
  return dailyUsageSeconds >= planConfig.dailyLimitSeconds
}

// Get price ID based on plan and billing period
export function getPriceId(plan: SubscriptionPlan, billingPeriod: 'monthly' | 'yearly'): string | null {
  if (plan === 'FREE') return null

  const planConfig = SUBSCRIPTION_PLANS[plan]
  return billingPeriod === 'monthly' ? planConfig.monthlyPriceId : planConfig.yearlyPriceId
}

// Get price amount based on plan and billing period
export function getPriceAmount(plan: SubscriptionPlan, billingPeriod: 'monthly' | 'yearly'): number {
  if (plan === 'FREE') return 0

  const planConfig = SUBSCRIPTION_PLANS[plan]
  return billingPeriod === 'monthly' ? planConfig.monthlyPrice : planConfig.yearlyPrice
}

// Calculate yearly savings
export function getYearlySavings(plan: SubscriptionPlan): number {
  if (plan === 'FREE') return 0

  const planConfig = SUBSCRIPTION_PLANS[plan]
  const monthlyTotal = planConfig.monthlyPrice * 12
  return monthlyTotal - planConfig.yearlyPrice
}

// Get savings percentage for yearly plan
export function getYearlySavingsPercentage(plan: SubscriptionPlan): number {
  if (plan === 'FREE') return 0

  const savings = getYearlySavings(plan)
  const planConfig = SUBSCRIPTION_PLANS[plan]
  const monthlyTotal = planConfig.monthlyPrice * 12

  return Math.round((savings / monthlyTotal) * 100)
}
