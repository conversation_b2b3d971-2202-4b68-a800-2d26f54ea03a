# SpeechPilot Stripe Setup Scripts

This directory contains scripts to set up Stripe products and prices for the SpeechPilot subscription system.

## 🚀 Quick Setup

### Prerequisites

1. **Stripe Account**: Make sure you have a Stripe account (test mode is fine for development)
2. **Stripe Secret Key**: Add your Stripe secret key to `.env.local`:
   ```
   STRIPE_SECRET_KEY="sk_test_..."
   ```

### Run the Setup Script

```bash
# Install dependencies (if not already done)
npm install

# Run the Stripe setup script
npm run setup-stripe
```

## 📋 What the Script Does

The `setup-stripe.js` script will:

1. **Create Stripe Products** for each subscription plan:
   - SpeechPilot Starter ($9.99/month, $99/year)
   - SpeechPilot Pro ($19.99/month, $199/year)
   - SpeechPilot Premium ($59.99/month, $559/year)
   - SpeechPilot Max ($129.99/month, $1099/year)

2. **Create Monthly and Yearly Prices** for each product

3. **Automatically Update .env.local** with the generated price IDs

4. **Save Results** to `scripts/stripe-setup-results.json` for reference

## 📊 Subscription Plans

Based on `Subscription.md`:

| Plan | Monthly Price | Yearly Price | Yearly Savings | Daily Limit | Platforms | Max Devices |
|------|---------------|--------------|----------------|-------------|-----------|-------------|
| **Free** | $0 | $0 | - | 5 minutes | None | 0 |
| **Starter** | $9.99 | $99 | $20.88 (17%) | 1 hour | Windows, macOS | 1 |
| **Pro** | $19.99 | $199 | $40.88 (17%) | 2 hours | All platforms | 2 |
| **Premium** | $59.99 | $559 | $160.88 (22%) | 8 hours | All platforms | 5 |
| **Max** | $129.99 | $1099 | $460.88 (30%) | Unlimited | All platforms | Unlimited |

## 🔧 After Running the Script

1. **Environment variables are automatically updated** in your `.env.local` file
2. **Update your Stripe webhook endpoint** if needed
3. **Test the subscription flow** in your application
4. **Check the generated results** in `scripts/stripe-setup-results.json`

## 📁 Generated Files

- `stripe-setup-results.json` - Contains all created product and price IDs
- Console output with environment variables to copy

## 🔄 Re-running the Script

If you need to re-run the script:

1. **Delete existing products** in your Stripe dashboard (optional)
2. **Run the script again** - it will create new products
3. **Update environment variables** with the new IDs

## 🛠️ Manual Setup (Alternative)

If you prefer to set up Stripe manually:

1. Go to your [Stripe Dashboard](https://dashboard.stripe.com/products)
2. Create products for each subscription plan
3. Create monthly recurring prices for each product
4. Copy the price IDs to your `.env.local` file

## 🔍 Troubleshooting

### "STRIPE_SECRET_KEY not found"
- Make sure you have added your Stripe secret key to `.env.local`
- The key should start with `sk_test_` (for test mode) or `sk_live_` (for live mode)

### "Invalid Stripe secret key format"
- Ensure your secret key is correctly formatted
- Test keys start with `sk_test_`, live keys start with `sk_live_`

### Network/API Errors
- Check your internet connection
- Verify your Stripe account is active
- Make sure you're using the correct API key

## 📞 Support

If you encounter issues:

1. Check the Stripe Dashboard for any created products
2. Verify your API key permissions
3. Review the console output for specific error messages
